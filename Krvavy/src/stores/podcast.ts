import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import axios from 'axios'
import { Preferences } from '@capacitor/preferences'

export interface Episode {
  id: string
  title: string
  description: string
  audioUrl: string
  imageUrl: string
  pubDate: string
  duration: string
  guid: string
}

export const usePodcastStore = defineStore('podcast', () => {
  // State
  const episodes = ref<Episode[]>([])
  const currentEpisode = ref<Episode | null>(null)
  const isPlaying = ref(false)
  const isLoading = ref(false)
  const savedEpisodes = ref<string[]>([])
  const downloadedEpisodes = ref<string[]>([])
  const currentTime = ref(0)
  const duration = ref(0)
  const volume = ref(1)
  
  // RSS Feed URL
  const RSS_FEED_URL = 'https://anchor.fm/s/8db2e1ec/podcast/rss'
  
  // GitHub Images Base URL
  const GITHUB_IMAGES_BASE = 'https://raw.githubusercontent.com/vldseman/krvavy-dobsinsky-images/main/podcast_images/'
  
  // Computed
  const savedEpisodesList = computed(() => {
    return episodes.value.filter(episode => savedEpisodes.value.includes(episode.id))
  })
  
  const progress = computed(() => {
    return duration.value > 0 ? (currentTime.value / duration.value) * 100 : 0
  })
  
  // Actions
  const loadRSSFeed = async () => {
    isLoading.value = true
    try {
      console.log('🔄 Loading RSS feed from:', RSS_FEED_URL)

      // Try direct fetch first
      let response
      try {
        response = await axios.get(RSS_FEED_URL, {
          timeout: 15000,
          headers: {
            'Accept': 'application/rss+xml, application/xml, text/xml, */*',
            'User-Agent': 'Krvavy-Dobsinsky-App/1.0',
            'Cache-Control': 'no-cache'
          },
          withCredentials: false
        })
        console.log('✅ RSS response received, parsing...')
      } catch (corsError) {
        console.warn('⚠️ Direct RSS fetch failed, trying CORS proxy:', corsError.message)

        // Try with CORS proxy
        const proxyUrl = `https://api.allorigins.win/get?url=${encodeURIComponent(RSS_FEED_URL)}`
        const proxyResponse = await axios.get(proxyUrl, {
          timeout: 15000,
          headers: {
            'Accept': 'application/json'
          }
        })

        if (proxyResponse.data && proxyResponse.data.contents) {
          response = { data: proxyResponse.data.contents }
          console.log('✅ RSS data received via proxy, parsing...')
        } else {
          throw new Error('No data received from proxy')
        }
      }

      // Parse XML using native DOMParser
      const parser = new DOMParser()
      const xmlDoc = parser.parseFromString(response.data, 'text/xml')

      // Check for parsing errors
      const parseError = xmlDoc.querySelector('parsererror')
      if (parseError) {
        throw new Error('XML parsing failed: ' + parseError.textContent)
      }

      const items = xmlDoc.querySelectorAll('item')
      
      episodes.value = Array.from(items).map((item: Element, index: number) => {
        const titleEl = item.querySelector('title')
        const title = titleEl?.textContent || ''
        const cleanTitle = title.replace(/[🔪💀👻🎭🌙⚡️🔥💯🎪🎨🎬#\[\]]/g, '').trim()
        const urlFriendlyTitle = cleanTitle
          .toLowerCase()
          .replace(/[^\w\s\-\(\)]/g, '')
          .replace(/\s+/g, '-')
          .replace(/--+/g, '-')
          .trim('-')

        const descriptionEl = item.querySelector('description')
        const guidEl = item.querySelector('guid')
        const enclosureEl = item.querySelector('enclosure')
        const pubDateEl = item.querySelector('pubDate')
        const durationEl = item.querySelector('itunes\\:duration, duration')

        return {
          id: guidEl?.textContent || `episode-${index}`,
          title: cleanTitle,
          description: descriptionEl?.textContent || '',
          audioUrl: enclosureEl?.getAttribute('url') || '',
          imageUrl: `${GITHUB_IMAGES_BASE}${urlFriendlyTitle}.png`,
          pubDate: pubDateEl?.textContent || '',
          duration: durationEl?.textContent || '00:00:00',
          guid: guidEl?.textContent || `episode-${index}`
        }
      })
      
      console.log('✅ Episodes loaded successfully:', episodes.value.length)

      // Load saved episodes from storage
      await loadSavedEpisodes()

    } catch (error) {
      console.error('❌ Error loading RSS feed:', error)

      // Fallback - create some dummy episodes for testing
      console.log('🔄 Creating fallback test episodes...')
      episodes.value = [
        {
          id: 'test-1',
          title: 'Krvavá Mária - Testovacia Epizóda',
          description: 'Testovacia epizóda pre debugging aplikácie. Táto epizóda slúži na overenie funkčnosti prehrávača a rozhrania.',
          audioUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
          imageUrl: 'https://raw.githubusercontent.com/vladimirSeman/Krvavyapp/main/images/krvavy-dobsinsky-logo.png',
          pubDate: new Date().toISOString(),
          duration: '10:00',
          guid: 'test-1'
        },
        {
          id: 'test-2',
          title: 'Strašidelný Zámok - Demo Epizóda',
          description: 'Druhá testovacia epizóda s horror tématikou. Obsahuje ukážku funkcionalít aplikácie.',
          audioUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
          imageUrl: 'https://raw.githubusercontent.com/vladimirSeman/Krvavyapp/main/images/krvavy-dobsinsky-logo.png',
          pubDate: new Date(Date.now() - 86400000).toISOString(), // Yesterday
          duration: '15:00',
          guid: 'test-2'
        },
        {
          id: 'test-3',
          title: 'Upírska Legenda - Ukážka',
          description: 'Tretia testovacia epizóda s retro horror atmosférou.',
          audioUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
          imageUrl: 'https://raw.githubusercontent.com/vladimirSeman/Krvavyapp/main/images/krvavy-dobsinsky-logo.png',
          pubDate: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
          duration: '12:30',
          guid: 'test-3'
        }
      ]
      console.log('✅ Fallback episodes created:', episodes.value.length)
    } finally {
      isLoading.value = false
    }
  }
  
  const playEpisode = (episode: Episode) => {
    currentEpisode.value = episode
    isPlaying.value = true
  }
  
  const pauseEpisode = () => {
    isPlaying.value = false
  }
  
  const stopEpisode = () => {
    currentEpisode.value = null
    isPlaying.value = false
    currentTime.value = 0
  }
  
  const saveEpisode = async (episodeId: string) => {
    if (!savedEpisodes.value.includes(episodeId)) {
      savedEpisodes.value.push(episodeId)
      await saveSavedEpisodes()
    }
  }
  
  const unsaveEpisode = async (episodeId: string) => {
    const index = savedEpisodes.value.indexOf(episodeId)
    if (index > -1) {
      savedEpisodes.value.splice(index, 1)
      await saveSavedEpisodes()
    }
  }
  
  const toggleSaveEpisode = async (episodeId: string) => {
    if (savedEpisodes.value.includes(episodeId)) {
      await unsaveEpisode(episodeId)
    } else {
      await saveEpisode(episodeId)
    }
  }
  
  const loadSavedEpisodes = async () => {
    try {
      const { value } = await Preferences.get({ key: 'savedEpisodes' })
      if (value) {
        savedEpisodes.value = JSON.parse(value)
      }
    } catch (error) {
      console.error('Error loading saved episodes:', error)
    }
  }
  
  const saveSavedEpisodes = async () => {
    try {
      await Preferences.set({
        key: 'savedEpisodes',
        value: JSON.stringify(savedEpisodes.value)
      })
    } catch (error) {
      console.error('Error saving episodes:', error)
    }
  }
  
  const setCurrentTime = (time: number) => {
    currentTime.value = time
  }
  
  const setDuration = (dur: number) => {
    duration.value = dur
  }
  
  const setVolume = (vol: number) => {
    volume.value = Math.max(0, Math.min(1, vol))
  }
  
  const getEpisodeById = (id: string): Episode | undefined => {
    return episodes.value.find(episode => episode.id === id)
  }
  
  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }
  
  return {
    // State
    episodes,
    currentEpisode,
    isPlaying,
    isLoading,
    savedEpisodes,
    downloadedEpisodes,
    currentTime,
    duration,
    volume,
    
    // Computed
    savedEpisodesList,
    progress,
    
    // Actions
    loadRSSFeed,
    playEpisode,
    pauseEpisode,
    stopEpisode,
    saveEpisode,
    unsaveEpisode,
    toggleSaveEpisode,
    setCurrentTime,
    setDuration,
    setVolume,
    getEpisodeById,
    formatDuration
  }
})

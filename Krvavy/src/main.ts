console.log('🔪 Starting Krvavý Do<PERSON>š<PERSON>ký app...')

// TypeScript declarations for window functions
declare global {
  interface Window {
    clearVueTimeout?: () => void
    showFallbackContent?: (message: string) => void
  }
}

// Global error handling
window.addEventListener('error', (e) => {
  console.error('🚨 Main.ts error:', e.error)
})

try {
  console.log('📦 Loading Vue and dependencies...')

  const { createApp } = await import('vue')
  const { createPinia } = await import('pinia')
  const { Capacitor } = await import('@capacitor/core')
  const { SplashScreen } = await import('@capacitor/splash-screen')
  const { StatusBar, Style } = await import('@capacitor/status-bar')
  const CapacitorAppModule = await import('@capacitor/app')
  const CapacitorApp = CapacitorAppModule.App

  console.log('📦 Imports loaded successfully')

  const App = (await import('./App.vue')).default
  const router = (await import('./router')).default

  await import('./style.css')

  console.log('🎨 Styles and components loaded')

  console.log('🏗 Creating Vue app...')
  const app = createApp(App)
  const pinia = createPinia()

  console.log('🔌 Setting up plugins...')
  app.use(pinia)
  app.use(router)

  console.log('✅ Vue app configured')

  // Initialize Capacitor plugins
  const initializeApp = async () => {
    if (Capacitor.isNativePlatform()) {
      // Configure status bar
      await StatusBar.setStyle({ style: Style.Dark })
      await StatusBar.setBackgroundColor({ color: '#2C1A0C' })

      // Handle app state changes
      CapacitorApp.addListener('appStateChange', ({ isActive }) => {
        console.log('App state changed. Is active?', isActive)
      })

      // Handle back button on Android
      CapacitorApp.addListener('backButton', ({ canGoBack }) => {
        if (!canGoBack) {
          CapacitorApp.exitApp()
        } else {
          window.history.back()
        }
      })

      // Hide splash screen
      await SplashScreen.hide()
    }
  }

  console.log('🚀 Mounting Vue app to #app...')
  try {
    app.mount('#app')
    console.log('✅ Vue app mounted successfully!')

    // Clear the timeout since Vue loaded successfully
    if (window.clearVueTimeout) {
      window.clearVueTimeout()
    }

    // Hide loading screen after successful mount
    setTimeout(() => {
      const loadingScreen = document.getElementById('loading-screen')
      if (loadingScreen) {
        loadingScreen.style.opacity = '0'
        loadingScreen.style.transition = 'opacity 0.5s ease-out'
        setTimeout(() => {
          loadingScreen.remove()
        }, 500)
      }
    }, 500)

  } catch (error) {
    console.error('❌ Error mounting Vue app:', error)
    throw error
  }

  console.log('🔧 Initializing Capacitor...')
  await initializeApp()

} catch (error) {
  console.error('🚨 Failed to initialize app:', error)
  // Show fallback content
  if (window.showFallbackContent) {
    window.showFallbackContent('App initialization failed: ' + error.message)
  }
}

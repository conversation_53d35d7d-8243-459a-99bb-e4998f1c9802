<!DOCTYPE html>
<html lang="sk">
<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/svg+xml" href="/vite.svg" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>🔪 Krvav<PERSON> Dobšinský</title>

  <!-- PWA Meta Tags -->
  <meta name="theme-color" content="#2C1A0C" />
  <meta name="mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
  <meta name="apple-mobile-web-app-title" content="Krvavý Dobšinský" />

  <!-- Manifest -->
  <link rel="manifest" href="/manifest.json" />

  <!-- Apple Touch Icons -->
  <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
  <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
  <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />

  <!-- Preload critical resources -->
  <link rel="modulepreload" href="/assets/main-CVz4cPHy.js" />
  <link rel="stylesheet" href="/assets/main-D-sgq-w0.css" />

  <style>
    /* Critical CSS for loading screen */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Creepster', cursive, system-ui, -apple-system, sans-serif;
      background: linear-gradient(135deg, #2C1A0C 0%, #1A0F06 100%);
      color: #F5E6D3;
      overflow-x: hidden;
      min-height: 100vh;
    }

    #app {
      min-height: 100vh;
      position: relative;
    }

    /* Loading Screen Styles */
    #loading-screen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #2C1A0C 0%, #1A0F06 100%);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      z-index: 9999;
      color: #F5E6D3;
    }

    .loading-content {
      text-align: center;
      max-width: 90%;
    }

    .loading-title {
      font-size: clamp(2rem, 8vw, 4rem);
      font-weight: bold;
      margin-bottom: 1rem;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
      color: #DAA520;
      animation: pulse 2s ease-in-out infinite;
    }

    .loading-subtitle {
      font-size: clamp(1rem, 4vw, 1.5rem);
      margin-bottom: 2rem;
      opacity: 0.8;
    }

    .loading-spinner {
      width: 60px;
      height: 60px;
      border: 4px solid rgba(218, 165, 32, 0.3);
      border-top: 4px solid #DAA520;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 1rem;
    }

    .loading-text {
      font-size: 1rem;
      opacity: 0.7;
      animation: fadeInOut 2s ease-in-out infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.7; }
    }

    @keyframes fadeInOut {
      0%, 100% { opacity: 0.7; }
      50% { opacity: 1; }
    }

    /* Fallback Content Styles */
    #fallback-content {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #2C1A0C 0%, #1A0F06 100%);
      color: #F5E6D3;
      padding: 2rem;
      text-align: center;
      z-index: 10000;
      overflow-y: auto;
    }

    .fallback-title {
      font-size: 2rem;
      color: #DAA520;
      margin-bottom: 1rem;
    }

    .fallback-message {
      font-size: 1.2rem;
      margin-bottom: 2rem;
      line-height: 1.6;
    }

    .fallback-actions {
      margin-top: 2rem;
    }

    .fallback-button {
      background: #DAA520;
      color: #2C1A0C;
      border: none;
      padding: 1rem 2rem;
      font-size: 1rem;
      border-radius: 8px;
      cursor: pointer;
      margin: 0.5rem;
      font-weight: bold;
      transition: all 0.3s ease;
    }

    .fallback-button:hover {
      background: #B8941C;
      transform: translateY(-2px);
    }

    #debug-info {
      margin-top: 2rem;
      padding: 1rem;
      background: rgba(0,0,0,0.3);
      border-radius: 8px;
      font-family: monospace;
      font-size: 0.9rem;
      text-align: left;
    }
  </style>
</head>
<body>
  <div id="app"></div>

  <!-- Main Application Script -->
  <script type="module" src="/assets/main-CVz4cPHy.js"></script>

</body>
</html>
